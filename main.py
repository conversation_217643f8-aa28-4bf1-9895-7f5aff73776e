# Para usar este código, você precisará:
# 1. Token de acesso do Hugging Face (gratuito)
# 2. API key da OpenAI (para Whisper API - versão paga)
# 3. Instalar as dependências: pip install pyannote.audio openai python-dotenv

import os
from dotenv import load_dotenv
from pyannote.audio import Pipeline
from openai import OpenAI

# Configurações iniciais
load_dotenv()
AUDIO_FILE = "consulta.mp3"  # Substitua pelo seu arquivo de áudio
HF_TOKEN = os.getenv("HF_API_KEY")  # Obtenha em: https://huggingface.co/settings/tokens
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")  # Obtenha em: https://platform.openai.com/api-keys

def diarizar_audio(audio_path, token):
    """Identifica falantes e intervalos de fala usando pyannote"""
    pipeline = Pipeline.from_pretrained(
        "pyannote/speaker-diarization-3.1",
        use_auth_token=token)
    
    diarizacao = pipeline(audio_path)
    return [(segment, _, speaker) for segment, _, speaker in diarizacao.itertracks(yield_label=True)]

def transcrever_audio(audio_path, api_key):
    """Transcreve o áudio completo usando Whisper API da OpenAI"""
    client = OpenAI(api_key=api_key)
    with open(audio_path, "rb") as audio_file:
        transcricao = client.audio.transcriptions.create(
            file=audio_file,
            model="whisper-1",
            response_format="verbose_json",
            timestamp_granularities=["segment"]
        )
    return transcricao.segments

def combinar_resultados(diarizacao, transcricao):
    """Combina diarização e transcrição para atribuir falantes aos textos"""
    resultado = []
    
    for segmento in transcricao:
        inicio = segmento['start']
        fim = segmento['end']
        texto = segmento['text']
        
        # Encontrar falante predominante no intervalo
        falantes = []
        for (intervalo, _, speaker) in diarizacao:
            if intervalo.start <= inicio and intervalo.end >= fim:
                falantes.append(speaker)
        
        # Escolher falante mais frequente no intervalo
        falante = max(set(falantes), key=falantes.count) if falantes else "DESCONHECIDO"
        
        resultado.append({
            'inicio': inicio,
            'fim': fim,
            'falante': falante,
            'texto': texto
        })
    
    return resultado

# Processamento principal
if __name__ == "__main__":
    if not all([HF_TOKEN, OPENAI_API_KEY]):
        raise ValueError("Configure as variáveis de ambiente primeiro!")

    print("Processando diarização...")
    diarizacao = diarizar_audio(AUDIO_FILE, HF_TOKEN)
    
    print("Transcrevendo áudio...")
    transcricao = transcrever_audio(AUDIO_FILE, OPENAI_API_KEY)
    
    print("Combinando resultados...")
    resultado_final = combinar_resultados(diarizacao, transcricao)
    
    # Exibir resultados formatados
    print("\nTRANSCRIÇÃO COM IDENTIFICAÇÃO DE FALANTES:")
    for item in resultado_final:
        print(f"\n[{item['falante']} - {item['inicio']:.1f}s a {item['fim']:.1f}s]:")
        print(item['texto'])