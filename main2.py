import os
import whisper

class AudioTranscriber:
    def __init__(self, model_size="small"):
        """
        Initializes the transcriber with a specific model size.
        Available models: tiny, base, small, medium, large
        """
        print(f"Loading Whisper model '{model_size}'...")
        self.model = whisper.load_model(model_size)
        print("Model loaded successfully!")

    def transcribe_audio(self, audio_path, language="pt"):
        """
        Transcribes the audio file specified by audio_path and returns the full result.
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        print(f"Transcribing audio: {audio_path}")
        result = self.model.transcribe(audio_path, language=language)
        return result

    def save_transcription(self, transcription_text, output_file):
        """
        Saves the transcription text to a .txt file.
        """
        with open(output_file, "w", encoding="utf-8") as file:
            file.write(transcription_text)
        print(f"Transcription saved to: {output_file}")

    def transcribe_and_save_from_directory(self, input_dir, output_dir, language="pt"):
        """
        Transcribes all audio files from input directory and saves to output directory.
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Iterate over all files in the input directory
        for file_name in os.listdir(input_dir):
            # Construct the full file path
            audio_path = os.path.join(input_dir, file_name)

            # Check if the file is an audio file
            if os.path.isfile(audio_path) and file_name.lower().endswith(('.mp3', '.mp4', '.wav', '.m4a')):
                # Get the base name of the audio file (without extension)
                base_name = os.path.splitext(file_name)[0]
                output_file = os.path.join(output_dir, f"{base_name}_transcription.txt")

                # Transcribe the audio and save the result
                result = self.transcribe_audio(audio_path, language)
                self.save_transcription(result["text"], output_file)

    def transcribe_with_timestamps(self, audio_path, language="pt"):
        """
        Transcribes audio and returns segments with timestamps.
        """
        result = self.transcribe_audio(audio_path, language)
        return result["segments"]


# Main execution
if __name__ == "__main__":
    # Configuration
    AUDIO_FILE = "/home/<USER>/repositorios/talk-transcription/audio_debate.mp3"  # Change this to your audio file
    MODEL_SIZE = "small"  # Options: tiny, base, small, medium, large
    
    try:
        # Initialize transcriber
        transcriber = AudioTranscriber(model_size=MODEL_SIZE)
        
        # Transcribe with timestamps
        segments = transcriber.transcribe_with_timestamps(AUDIO_FILE, language="pt")
        
        # Display transcription with timestamps
        print("\n" + "="*50)
        print("TRANSCRIÇÃO COM TIMESTAMPS:")
        print("="*50)
        
        for segment in segments:
            start_time = segment['start']
            end_time = segment['end']
            text = segment['text'].strip()
            print(f"[{start_time:6.1f}s - {end_time:6.1f}s]: {text}")
        
        # Save full transcription to file
        full_result = transcriber.transcribe_audio(AUDIO_FILE, language="pt")
        output_file = "transcription.txt"
        transcriber.save_transcription(full_result["text"], output_file)
        
        print(f"\nFull transcription saved to: {output_file}")
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print(f"Please make sure the audio file '{AUDIO_FILE}' exists in the current directory.")
    except Exception as e:
        print(f"An error occurred: {e}")
